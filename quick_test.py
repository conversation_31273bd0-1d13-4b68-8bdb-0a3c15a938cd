"""
快速测试脚本 - 验证DSA数据收集器的实时数据流功能
"""

import time
import os
from data_collector_3s import DSADataCollector


def test_real_time_stream():
    """测试实时数据流功能"""
    print("快速测试DSA数据收集器的实时数据流功能")
    print("="*50)
    
    # 检查DLL文件
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"错误：找不到DLL文件 {dll_path}")
        return False
    
    try:
        # 创建数据收集器
        print("1. 创建DSA数据收集器...")
        collector = DSADataCollector(dll_path)
        
        # 启用实时数据流
        print("2. 启用实时数据流...")
        collector.enable_real_time_streaming()
        
        # 初始化SDK
        print("3. 初始化SDK...")
        if not collector.initialize_sdk():
            print("SDK初始化失败")
            return False
        
        # 启动数据收集
        print("4. 启动数据收集...")
        if not collector.start_collection():
            print("数据收集启动失败")
            return False
        
        # 等待数据收集稳定
        print("5. 等待数据收集稳定（3秒）...")
        time.sleep(3.0)
        
        # 测试实时数据读取
        print("6. 测试实时数据读取...")
        data_count = 0
        displacement_count = 0
        velocity_count = 0
        
        test_duration = 5  # 测试5秒
        start_time = time.time()
        
        while time.time() - start_time < test_duration:
            data = collector.get_real_time_data(timeout=0.001)
            if data is not None:
                data_count += 1
                if data['type'] == 'displacement':
                    displacement_count += 1
                elif data['type'] == 'velocity':
                    velocity_count += 1
                
                # 每500个数据点显示一次
                if data_count % 500 == 0:
                    print(f"  已读取 {data_count} 个数据点 (位移:{displacement_count}, 速度:{velocity_count})")
            
            time.sleep(0.001)  # 1ms间隔
        
        # 显示测试结果
        print(f"\n测试结果:")
        print(f"  测试时长: {test_duration} 秒")
        print(f"  总数据点数: {data_count}")
        print(f"  位移数据点数: {displacement_count}")
        print(f"  速度数据点数: {velocity_count}")
        print(f"  平均采样率: {data_count/test_duration:.1f} Hz")
        print(f"  队列剩余大小: {collector.get_real_time_queue_size()}")
        
        # 停止数据收集
        print("7. 停止数据收集...")
        collector.is_running = False
        collector.sdk.stop()
        collector.sdk.unInitialize()
        
        success = data_count > 0
        if success:
            print("✅ 实时数据流测试成功！")
        else:
            print("❌ 实时数据流测试失败 - 未读取到数据")
        
        return success
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False


def main():
    """主函数"""
    print("DSA数据收集器实时数据流快速测试")
    print("此测试验证修改后的data_collector_3s.py是否正常工作")
    print()
    
    success = test_real_time_stream()
    
    print("\n" + "="*50)
    if success:
        print("🎉 快速测试通过！")
        print("现在可以运行以下程序进行完整测试：")
        print("  python test_integration.py      # 集成测试")
        print("  python integrated_real_time_demo.py  # 完整演示")
    else:
        print("⚠️ 快速测试失败")
        print("请检查：")
        print("  1. DSA设备是否正确连接")
        print("  2. DLL文件路径是否正确")
        print("  3. 网络配置是否正确")
    print("="*50)


if __name__ == "__main__":
    main()
