import ctypes
from ctypes import *
import time
import os
from collections import Counter

class DataAnalyzer:
    def __init__(self, dll_path):
        """数据重复分析器"""
        self.sdk = ctypes.cdll.LoadLibrary(dll_path)
        
        # 定义回调函数类型
        self.CALLBACK = CFUNCTYPE(None, c_int, c_int, c_int, c_int, POINTER(c_float), c_int)
        self._callback_func = self.CALLBACK(self._data_callback)
        
        # 分析变量
        self.callback_count = 0
        self.start_time = None
        self.is_running = True
        self.all_data = []
        
        # 枚举定义
        self.OutDataType = {'odtDisplacement': 0x02, 'odtNoOutput': 0x04}
        self.OutputFilter = {'of2k': 0x01}
        self.VelocityRange = {'vr1': 0x01}
        self.DisplacementRange = {'dr1': 0x01}
        self.DeviceType = {'DT3M': 0x01}
        self.LaserWaveLength = {'LW_632_8': 0x01}

    def _data_callback(self, dataType, sRate, vRange, dRange, data_ptr, dataLen):
        """数据回调函数 - 收集所有数据进行分析"""
        if not self.is_running:
            return
            
        self.callback_count += 1
        current_time = time.time()
        
        if self.start_time is None:
            self.start_time = current_time
        
        # 只收集2秒的数据进行分析
        if current_time - self.start_time >= 2.0:
            self.is_running = False
            return
        
        # 转换数据
        array_type = c_float * dataLen
        data = cast(data_ptr, POINTER(array_type)).contents
        values = list(data)
        
        # 存储所有数据点
        for i, value in enumerate(values):
            timestamp = current_time + (i / sRate)
            self.all_data.append({
                'timestamp': timestamp,
                'value': value,
                'callback_id': self.callback_count,
                'index_in_callback': i
            })
        
        print(f"回调 #{self.callback_count}: 收集了 {dataLen} 个数据点")

    def analyze_data(self):
        """分析数据重复模式"""
        if len(self.all_data) == 0:
            print("没有收集到数据")
            return
        
        print(f"\n=== 数据分析结果 ===")
        print(f"总数据点数: {len(self.all_data)}")
        print(f"时间跨度: {self.all_data[-1]['timestamp'] - self.all_data[0]['timestamp']:.3f} 秒")
        
        # 分析数值重复情况
        values = [data['value'] for data in self.all_data]
        value_counts = Counter([f"{v:.6f}" for v in values])
        unique_values = len(value_counts)
        
        print(f"唯一数值数量: {unique_values}")
        print(f"重复率: {(len(values) - unique_values) / len(values) * 100:.1f}%")
        
        # 显示最常见的数值
        print(f"\n最常见的5个数值:")
        for value, count in value_counts.most_common(5):
            percentage = count / len(values) * 100
            print(f"  {value}: {count}次 ({percentage:.1f}%)")
        
        # 分析连续重复
        self.analyze_consecutive_duplicates()
        
        # 模拟0.5ms采样
        self.simulate_sampling()

    def analyze_consecutive_duplicates(self):
        """分析连续重复数据"""
        print(f"\n=== 连续重复分析 ===")
        
        consecutive_groups = []
        current_group = [self.all_data[0]]
        
        for i in range(1, len(self.all_data)):
            if abs(self.all_data[i]['value'] - self.all_data[i-1]['value']) < 1e-6:
                current_group.append(self.all_data[i])
            else:
                if len(current_group) >= 5:  # 连续5个或更多相同值
                    consecutive_groups.append(current_group)
                current_group = [self.all_data[i]]
        
        # 检查最后一组
        if len(current_group) >= 5:
            consecutive_groups.append(current_group)
        
        print(f"发现 {len(consecutive_groups)} 组连续重复数据（≥5个相同值）")
        
        for i, group in enumerate(consecutive_groups[:3]):  # 只显示前3组
            print(f"\n第{i+1}组连续重复:")
            print(f"  数值: {group[0]['value']:.6f}")
            print(f"  连续数量: {len(group)}")
            print(f"  时间跨度: {group[-1]['timestamp'] - group[0]['timestamp']:.6f} 秒")
            print(f"  开始时间: {group[0]['timestamp']:.6f}")
            print(f"  结束时间: {group[-1]['timestamp']:.6f}")

    def simulate_sampling(self):
        """模拟0.5ms间隔采样"""
        print(f"\n=== 模拟0.5ms采样 ===")
        
        if len(self.all_data) == 0:
            return
        
        sampling_interval = 0.0005  # 0.5ms
        start_time = self.all_data[0]['timestamp']
        sampled_data = []
        
        target_time = start_time
        for data_point in self.all_data:
            if data_point['timestamp'] >= target_time:
                sampled_data.append(data_point)
                target_time += sampling_interval
                
                if len(sampled_data) >= 20:  # 只取前20个点进行分析
                    break
        
        print(f"模拟采样得到 {len(sampled_data)} 个数据点:")
        print("序号  时间戳差值(ms)  数值")
        print("-" * 40)
        
        for i, data in enumerate(sampled_data):
            time_diff = (data['timestamp'] - start_time) * 1000  # 转换为毫秒
            print(f"{i:2d}    {time_diff:8.3f}      {data['value']:.6f}")
        
        # 检查模拟采样中的重复
        sampled_values = [data['value'] for data in sampled_data]
        sampled_counts = Counter([f"{v:.6f}" for v in sampled_values])
        
        print(f"\n模拟采样中的重复情况:")
        for value, count in sampled_counts.items():
            if count >= 3:
                print(f"  数值 {value} 出现 {count} 次")

    def run_analysis(self):
        """运行分析"""
        print("数据重复分析器")
        print("=" * 40)
        
        # 初始化SDK
        result = self.sdk.initialize()
        if result != 0:
            print(f"初始化失败: {result}")
            return False
        
        # 配置
        self.sdk.setBindInfo(b"0.0.0.0", 62618)
        self.sdk.setBoardcastInfo(b"255.255.255.255", 63082)
        self.sdk.setDeviceType(self.DeviceType['DT3M'])
        self.sdk.setDeviceId(0)
        self.sdk.setLaserWaveLength(self.LaserWaveLength['LW_632_8'])
        self.sdk.setDataCallBack(self._callback_func)
        self.sdk.setDataLength(256)  # 小数据包
        self.sdk.setOutputFilter(self.OutputFilter['of2k'])
        self.sdk.setVelocityRange(self.VelocityRange['vr1'])
        self.sdk.setDisplacementRange(self.DisplacementRange['dr1'])
        
        # 设置输出位移数据
        self.sdk.setOutDataType(self.OutDataType['odtNoOutput'])
        time.sleep(0.05)
        self.sdk.setOutDataType(self.OutDataType['odtDisplacement'])
        
        # 启动
        result = self.sdk.start()
        if result != 0:
            print(f"启动失败: {result}")
            return False
        
        print("开始收集数据进行分析...")
        
        try:
            # 收集2秒数据
            while self.is_running:
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("\n用户中断...")
        finally:
            self.sdk.stop()
            self.sdk.unInitialize()
        
        # 分析数据
        self.analyze_data()
        
        return True


def main():
    """主函数"""
    print("DSANet 数据重复分析工具")
    print("用于分析为什么会出现连续相同的数据")
    print("-" * 50)
    
    # 检查DLL文件
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"错误：找不到DLL文件 {dll_path}")
        return
    
    # 运行分析
    analyzer = DataAnalyzer(dll_path)
    analyzer.run_analysis()


if __name__ == "__main__":
    main()
