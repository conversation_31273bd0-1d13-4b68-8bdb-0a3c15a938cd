import ctypes
from ctypes import *
import time
import os

class TimestampDebugger:
    def __init__(self, dll_path):
        """时间戳调试器"""
        self.sdk = ctypes.cdll.LoadLibrary(dll_path)
        
        # 定义回调函数类型
        self.CALLBACK = CFUNCTYPE(None, c_int, c_int, c_int, c_int, POINTER(c_float), c_int)
        self._callback_func = self.CALLBACK(self._data_callback)
        
        # 调试变量
        self.callback_count = 0
        self.start_time = None
        self.is_running = True
        
        # 枚举定义
        self.OutDataType = {'odtDisplacement': 0x02, 'odtNoOutput': 0x04}
        self.OutputFilter = {'of2k': 0x01}
        self.VelocityRange = {'vr1': 0x01}
        self.DisplacementRange = {'dr1': 0x01}
        self.DeviceType = {'DT3M': 0x01}
        self.LaserWaveLength = {'LW_632_8': 0x01}

    def _data_callback(self, dataType, sRate, vRange, dRange, data_ptr, dataLen):
        """调试回调函数"""
        if not self.is_running:
            return
            
        self.callback_count += 1
        current_time = time.time()
        
        if self.start_time is None:
            self.start_time = current_time
        
        # 只处理前3次回调进行调试
        if self.callback_count > 3:
            self.is_running = False
            return
        
        # 转换数据
        array_type = c_float * dataLen
        data = cast(data_ptr, POINTER(array_type)).contents
        values = list(data)
        
        print(f"\n=== 回调 #{self.callback_count} ===")
        print(f"回调时间: {current_time:.9f}")
        print(f"采样率: {sRate} Hz")
        print(f"数据长度: {dataLen}")
        print(f"理论时间间隔: {1/sRate:.9f} 秒")
        
        # 分析前10个数据点的时间戳
        print("\n前10个数据点的时间戳计算:")
        print("序号    时间戳(9位小数)    与回调时间差值    数值")
        print("-" * 60)
        
        filename = f"debug_callback_{self.callback_count}_timestamps.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# 回调 #{self.callback_count} 时间戳调试\n")
            f.write(f"# 回调时间: {current_time:.9f}\n")
            f.write(f"# 采样率: {sRate} Hz\n")
            f.write(f"# 理论时间间隔: {1/sRate:.9f} 秒\n")
            f.write(f"# 数据长度: {dataLen}\n")
            f.write("# 序号 时间戳(9位小数) 与回调时间差值 数值\n")
            f.write("# " + "-" * 50 + "\n")
            
            for i in range(min(10, len(values))):
                timestamp = current_time + (i / sRate)
                time_diff = i / sRate
                value = values[i]
                
                print(f"{i:4d}    {timestamp:.9f}    {time_diff:.9f}    {value:.6f}")
                f.write(f"{i:4d} {timestamp:.9f} {time_diff:.9f} {value:.6f}\n")
        
        print(f"\n详细数据已保存到: {filename}")
        
        # 检查时间戳差异
        if dataLen >= 3:
            ts1 = current_time + (0 / sRate)
            ts2 = current_time + (1 / sRate)
            ts3 = current_time + (2 / sRate)
            
            print(f"\n时间戳差异分析:")
            print(f"第1个点: {ts1:.9f}")
            print(f"第2个点: {ts2:.9f}")
            print(f"第3个点: {ts3:.9f}")
            print(f"差值1-2: {ts2-ts1:.9f}")
            print(f"差值2-3: {ts3-ts2:.9f}")
            
            # 检查6位小数精度下的显示
            print(f"\n6位小数精度显示:")
            print(f"第1个点: {ts1:.6f}")
            print(f"第2个点: {ts2:.6f}")
            print(f"第3个点: {ts3:.6f}")
            
            if f"{ts1:.6f}" == f"{ts2:.6f}":
                print("⚠️ 在6位小数精度下，前两个时间戳相同！")
            if f"{ts2:.6f}" == f"{ts3:.6f}":
                print("⚠️ 在6位小数精度下，第2和第3个时间戳相同！")

    def run_debug(self):
        """运行调试"""
        print("时间戳调试器")
        print("=" * 40)
        
        # 初始化SDK
        result = self.sdk.initialize()
        if result != 0:
            print(f"初始化失败: {result}")
            return False
        
        # 配置
        self.sdk.setBindInfo(b"0.0.0.0", 62618)
        self.sdk.setBoardcastInfo(b"255.255.255.255", 63082)
        self.sdk.setDeviceType(self.DeviceType['DT3M'])
        self.sdk.setDeviceId(0)
        self.sdk.setLaserWaveLength(self.LaserWaveLength['LW_632_8'])
        self.sdk.setDataCallBack(self._callback_func)
        self.sdk.setDataLength(256)  # 小数据包
        self.sdk.setOutputFilter(self.OutputFilter['of2k'])
        self.sdk.setVelocityRange(self.VelocityRange['vr1'])
        self.sdk.setDisplacementRange(self.DisplacementRange['dr1'])
        
        # 设置输出位移数据
        self.sdk.setOutDataType(self.OutDataType['odtNoOutput'])
        time.sleep(0.05)
        self.sdk.setOutDataType(self.OutDataType['odtDisplacement'])
        
        # 启动
        result = self.sdk.start()
        if result != 0:
            print(f"启动失败: {result}")
            return False
        
        print("开始调试，将分析前3次回调的时间戳...")
        
        try:
            # 等待3次回调完成
            while self.is_running and self.callback_count < 3:
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("\n用户中断...")
        finally:
            self.sdk.stop()
            self.sdk.unInitialize()
        
        print("\n" + "=" * 40)
        print("调试完成！")
        print(f"共处理了 {self.callback_count} 次回调")
        print("详细时间戳数据已保存到 debug_callback_X_timestamps.txt 文件")
        
        return True


def main():
    """主函数"""
    print("DSANet 时间戳调试工具")
    print("用于分析为什么前几个数据点时间戳相同")
    print("-" * 50)
    
    # 检查DLL文件
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"错误：找不到DLL文件 {dll_path}")
        return
    
    # 运行调试
    debugger = TimestampDebugger(dll_path)
    debugger.run_debug()


if __name__ == "__main__":
    main()
