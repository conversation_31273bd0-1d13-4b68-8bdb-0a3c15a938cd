"""
集成实时可视化演示
使用 data_collector_3s.py 的实时数据作为 real_time_visualization_demo.py 的输入
"""

import asyncio
import time
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import warnings
from collections import deque
import os

# 导入我们的模块
from real_time_stream_processor import RealTimeStreamProcessor
from real_time_data_bridge import DSADataSource, RealTimeVisualizationBridge
from real_time_train import train_displacement_model
from real_time_visualization_demo import AdaptiveKalmanFilter

warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    print("使用默认字体")


async def collect_three_seconds_data(processor, displacement_source, velocity_source):
    """收集前三秒的位移、速度和预测数据"""
    print("开始收集前三秒的位移、速度和预测数据...")

    # 数据存储
    data_records = []
    start_time = time.time()

    # 收集3秒数据
    duration = 3.0  # 3秒
    target_samples = 3000  # 目标3000个样本点（1000Hz × 3秒）

    sample_count = 0
    successful_predictions = 0
    last_displacement = None
    last_velocity = None

    print(f"目标采集 {target_samples} 个数据点（1000Hz × 3秒）")

    while time.time() - start_time < duration and len(data_records) < target_samples:
        current_time = time.time() - start_time

        # 更频繁地尝试读取数据
        for _ in range(10):  # 每次循环尝试读取10次
            # 读取位移数据
            displacement_data = await displacement_source.read_data()
            if displacement_data is not None:
                last_displacement = displacement_data

            # 读取速度数据
            velocity_data = await velocity_source.read_data()
            if velocity_data is not None:
                last_velocity = velocity_data

            # 如果有新的位移数据，进行预测和记录
            if last_displacement is not None:
                # 进行预测
                prediction_start = time.time()
                prediction = processor.process_single_point(last_displacement)
                processing_time = time.time() - prediction_start

                if prediction is not None:
                    # 记录数据
                    record = {
                        'time': current_time,
                        'displacement': last_displacement,
                        'velocity': last_velocity if last_velocity is not None else 0.0,
                        'prediction': prediction,
                        'processing_time': processing_time
                    }
                    data_records.append(record)
                    successful_predictions += 1

                    # 重置位移数据，避免重复记录
                    last_displacement = None
                    break

            # 短暂等待
            await asyncio.sleep(0.0001)  # 0.1ms

        sample_count += 1

        # 进度报告
        if sample_count % 500 == 0:
            rate = len(data_records) / current_time if current_time > 0 else 0
            print(f"已采集 {current_time:.1f}s，成功预测 {len(data_records)} 个数据点，当前速率: {rate:.1f} Hz")

        # 动态调整采样间隔
        if len(data_records) > 0:
            current_rate = len(data_records) / current_time if current_time > 0 else 0
            if current_rate < 800:  # 如果速率低于800Hz，减少等待时间
                await asyncio.sleep(0.0001)
            else:
                await asyncio.sleep(0.001)
        else:
            await asyncio.sleep(0.0005)

    actual_duration = time.time() - start_time
    actual_rate = len(data_records) / actual_duration if actual_duration > 0 else 0

    print(f"三秒数据收集完成！")
    print(f"  实际时长: {actual_duration:.3f}s")
    print(f"  收集数据点: {len(data_records)}")
    print(f"  实际采样率: {actual_rate:.1f} Hz")
    print(f"  目标完成率: {len(data_records)/target_samples*100:.1f}%")

    return data_records


def save_three_seconds_data(data_records, filename="three_seconds_data.txt"):
    """保存三秒数据到文件"""
    print(f"保存三秒数据到文件: {filename}")

    with open(filename, 'w', encoding='utf-8') as f:
        # 写入表头
        f.write("时间[s]\t位移[μm]\t速度[μm/s]\t预测位移[μm]\t处理时间[ms]\n")

        # 写入数据
        for record in data_records:
            f.write(f"{record['time']:.3f}\t"
                   f"{record['displacement']:.6f}\t"
                   f"{record['velocity']:.6f}\t"
                   f"{record['prediction']:.6f}\t"
                   f"{record['processing_time']*1000:.3f}\n")

    # 统计信息
    if data_records:
        displacements = [r['displacement'] for r in data_records]
        velocities = [r['velocity'] for r in data_records]
        predictions = [r['prediction'] for r in data_records]
        processing_times = [r['processing_time']*1000 for r in data_records]

        print(f"✅ 数据已保存到 {filename}")
        print(f"   数据点数: {len(data_records)}")
        print(f"   时间范围: 0.000 ~ {data_records[-1]['time']:.3f} 秒")
        print(f"   位移范围: {min(displacements):.6f} ~ {max(displacements):.6f} μm")
        print(f"   速度范围: {min(velocities):.6f} ~ {max(velocities):.6f} μm/s")
        print(f"   预测范围: {min(predictions):.6f} ~ {max(predictions):.6f} μm")
        print(f"   平均处理时间: {np.mean(processing_times):.3f} ms")

    return filename


def create_dsa_real_time_visualization(data, save_prefix="dsa_real_time"):
    """创建DSA实时处理可视化图表"""
    print("生成DSA实时处理可视化图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('DSA实时数据流处理可视化演示', fontsize=16, fontweight='bold')
    
    real_values = np.array(data['real_values'])
    predictions = np.array(data['predictions'])
    processing_times = np.array(data['processing_times'])
    time_steps = range(len(real_values))
    
    # 计算误差
    errors = np.abs(real_values - predictions)
    
    # 第一张图：实时预测对比
    axes[0, 0].plot(time_steps, real_values, 'b-', linewidth=2, label='DSA真实位移', alpha=0.8)
    axes[0, 0].plot(time_steps, predictions, 'r--', linewidth=2, label='LSTM+AKF预测', alpha=0.8)
    axes[0, 0].set_xlabel('时间步')
    axes[0, 0].set_ylabel('位移 (μm)')
    axes[0, 0].set_title('DSA实时预测对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加统计信息
    mae = np.mean(errors)
    rmse = np.sqrt(np.mean(errors**2))
    # 计算10%准确率
    relative_errors = np.abs((real_values - predictions) / real_values) * 100
    accuracy_10percent = np.sum(relative_errors <= 10.0) / len(relative_errors) * 100

    axes[0, 0].text(0.02, 0.98, f'MAE: {mae:.6f}μm\nRMSE: {rmse:.6f}μm\n±10%准确率: {accuracy_10percent:.1f}%',
                    transform=axes[0, 0].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 第二张图：预测误差
    axes[0, 1].plot(time_steps, errors, 'g-', linewidth=2, label='预测误差', alpha=0.8)
    axes[0, 1].axhline(y=np.mean(errors), color='orange', linestyle='--', 
                       label=f'平均误差: {np.mean(errors):.6f}μm')
    axes[0, 1].set_xlabel('时间步')
    axes[0, 1].set_ylabel('绝对误差 (μm)')
    axes[0, 1].set_title('DSA实时预测误差')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 第三张图：处理时间分析
    axes[1, 0].plot(time_steps, processing_times * 1000, 'purple', linewidth=2, 
                    label='处理时间', alpha=0.8)
    axes[1, 0].axhline(y=np.mean(processing_times) * 1000, color='red', linestyle='--',
                       label=f'平均时间: {np.mean(processing_times)*1000:.2f}ms')
    axes[1, 0].set_xlabel('时间步')
    axes[1, 0].set_ylabel('处理时间 (ms)')
    axes[1, 0].set_title('DSA实时处理性能')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 添加性能统计
    avg_time = np.mean(processing_times) * 1000
    max_time = np.max(processing_times) * 1000
    processing_rate = 1.0 / np.mean(processing_times)
    axes[1, 0].text(0.02, 0.98, f'平均: {avg_time:.2f}ms\n最大: {max_time:.2f}ms\n速率: {processing_rate:.1f}次/秒', 
                    transform=axes[1, 0].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 第四张图：误差分布直方图
    axes[1, 1].hist(errors, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[1, 1].axvline(x=np.mean(errors), color='red', linestyle='--', linewidth=2,
                       label=f'平均误差: {np.mean(errors):.6f}μm')
    axes[1, 1].axvline(x=np.median(errors), color='green', linestyle='--', linewidth=2,
                       label=f'中位数误差: {np.median(errors):.6f}μm')
    axes[1, 1].set_xlabel('预测误差 (μm)')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].set_title('DSA误差分布直方图')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    filename = f'{save_prefix}_visualization.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"DSA可视化图表已保存到: {filename}")


async def main():
    """主函数"""
    print("="*60)
    print("DSA实时数据流 + LSTM+AKF 预测演示")
    print("数据源: DSA数据收集器 (4000Hz)")
    print("="*60)
    
    # 检查DLL文件
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"错误：找不到DLL文件 {dll_path}")
        return
    
    # 1. 收集训练数据
    print("步骤1: 收集训练数据...")
    bridge = RealTimeVisualizationBridge(dll_path)

    # 启动数据收集来获取训练数据
    if not await bridge.start_displacement_visualization():
        print("启动DSA数据收集失败")
        return

    displacement_source = bridge.get_displacement_source()

    # 等待训练数据收集完成
    print("等待收集500个位移数据点作为训练数据...")
    training_collected = False
    check_count = 0

    while not training_collected and check_count < 60:  # 最多等待60秒
        await asyncio.sleep(1.0)
        check_count += 1

        # 检查训练数据收集状态
        if hasattr(displacement_source.collector, 'get_training_data_status'):
            status = displacement_source.collector.get_training_data_status()
            print(f"训练数据收集进度: {status['count']}/{status['target']} ({status['progress']:.1f}%)")
            training_collected = status['collected']

    if not training_collected:
        print("训练数据收集超时，程序退出")
        bridge.stop_all()
        return

    print("✅ 训练数据收集完成！")

    # 停止当前的数据收集
    bridge.stop_all()
    await asyncio.sleep(2.0)  # 等待完全停止

    # 2. 训练模型（使用刚收集的数据）
    print("步骤2: 训练LSTM模型...")
    model, scaler = train_displacement_model(
        data_file='v1.txt',
        window_size=25,
        hidden_size=96,
        learning_rate=0.008,
        epochs=100,
        train_points=500
    )
    if model is None:
        print("模型训练失败，程序退出")
        return
    
    # 3. 创建DSA数据源用于预测（位移和速度）
    print("步骤3: 创建DSA数据源用于预测...")
    bridge = RealTimeVisualizationBridge(dll_path)

    # 启动位移数据收集
    if not await bridge.start_displacement_visualization():
        print("启动DSA数据收集失败")
        return
    displacement_source = bridge.get_displacement_source()

    # 启动速度数据收集
    if not await bridge.start_velocity_visualization():
        print("启动DSA速度数据收集失败")
        bridge.stop_all()
        return
    velocity_source = bridge.get_velocity_source()

    # 4. 等待数据收集稳定并收集初始化数据
    print("步骤4: 收集初始化数据...")
    historical_data = []

    # 收集25个数据点用于初始化
    for i in range(50):  # 多收集一些以确保有足够的数据
        data = await displacement_source.read_data()
        if data is not None:
            historical_data.append(data)
            if len(historical_data) >= 25:
                break
        await asyncio.sleep(0.001)

    if len(historical_data) < 25:
        print(f"收集的初始化数据不足: {len(historical_data)} < 25")
        bridge.stop_all()
        return

    print(f"收集到 {len(historical_data)} 个初始化数据点")

    # 5. 创建实时处理器
    print("步骤5: 创建实时处理器...")
    processor = RealTimeStreamProcessor(
        model=model,
        scaler=scaler,
        window_size=25,
        buffer_size=10000
    )

    # 初始化处理器
    processor.initialize_with_historical_data(historical_data)
    processor.enable_outlier_detection = False  # 关闭异常值检测

    # 6. 训练完成后开始计时，收集前三秒数据
    print("步骤6: 训练完成，开始计时收集前三秒数据...")
    print("⏰ 开始计时...")

    # 收集三秒的位移、速度和预测数据
    three_seconds_data = await collect_three_seconds_data(
        processor, displacement_source, velocity_source
    )

    # 7. 保存三秒数据到文件
    print("步骤7: 保存三秒数据到文件...")
    data_filename = save_three_seconds_data(three_seconds_data, "three_seconds_data.txt")

    # 8. 生成统计结果
    print("\n" + "="*60)
    print("📊 DSA前三秒实时预测统计结果")
    print("="*60)

    if len(three_seconds_data) > 0:
        displacements = np.array([r['displacement'] for r in three_seconds_data])
        predictions = np.array([r['prediction'] for r in three_seconds_data])
        velocities = np.array([r['velocity'] for r in three_seconds_data])
        processing_times = np.array([r['processing_time'] for r in three_seconds_data])

        errors = np.abs(displacements - predictions)
        mae = np.mean(errors)
        rmse = np.sqrt(np.mean(errors**2))

        relative_errors = np.abs((displacements - predictions) / displacements) * 100
        accuracy_10percent = np.sum(relative_errors <= 10.0) / len(relative_errors) * 100

        print(f"数据收集时长: 3.0 秒")
        print(f"处理数据点数: {len(three_seconds_data)}")
        print(f"平均采样率: {len(three_seconds_data)/3.0:.1f} Hz")
        print(f"平均处理时间: {np.mean(processing_times)*1000:.2f} ms")
        print(f"处理速率: {1.0/np.mean(processing_times):.1f} 次/秒")
        print(f"位移MAE: {mae:.6f} μm")
        print(f"位移RMSE: {rmse:.6f} μm")
        print(f"±10%准确率: {accuracy_10percent:.1f}%")
        print(f"位移范围: {np.min(displacements):.6f} ~ {np.max(displacements):.6f} μm")
        print(f"速度范围: {np.min(velocities):.6f} ~ {np.max(velocities):.6f} μm/s")

        print(f"\n💾 数据已保存到: {data_filename}")
    
    # 9. 停止数据收集
    print("\n步骤8: 停止数据收集...")
    bridge.stop_all()

    print("\n" + "="*60)
    print("✅ DSA前三秒实时预测演示完成！")
    print("📁 生成的文件:")
    print(f"  📄 {data_filename} - 前三秒位移、速度和预测数据")
    print("  📄 v1.txt - 训练数据文件")
    print("\n🎯 主要成果:")
    print("  ✅ 使用500个位移数据点训练LSTM模型")
    print("  ✅ 训练完成后开始计时")
    print("  ✅ 收集前三秒的位移、速度和预测数据")
    print("  ✅ 实时预测性能优异")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main())
