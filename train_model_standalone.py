"""
独立的模型训练脚本
用于训练LSTM模型并保存到文件
"""

import os
from real_time_train import train_displacement_model, save_model, load_model, get_model_info


def main():
    """主训练函数"""
    print("="*60)
    print("LSTM位移预测模型训练程序")
    print("="*60)
    
    # 检查训练数据文件
    data_file = 'v1.txt'
    if not os.path.exists(data_file):
        print(f"错误：找不到训练数据文件 {data_file}")
        print("请先运行 collect_training_data.py 收集训练数据")
        return False
    
    print(f"找到训练数据文件: {data_file}")
    
    # 训练参数
    training_params = {
        'data_file': data_file,
        'window_size': 25,
        'hidden_size': 96,
        'learning_rate': 0.008,
        'epochs': 100,
        'train_points': 500
    }
    
    print("训练参数:")
    for key, value in training_params.items():
        print(f"  {key}: {value}")
    
    print("\n开始训练模型...")
    
    # 训练模型
    model, scaler = train_displacement_model(**training_params)
    
    if model is None or scaler is None:
        print("❌ 模型训练失败")
        return False
    
    print("✅ 模型训练成功！")
    
    # 显示模型信息
    info = get_model_info(model, scaler)
    print("\n模型信息:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    # 保存模型
    print("\n保存模型...")
    model_path = 'displacement_model.pth'
    scaler_path = 'displacement_scaler.pkl'
    
    if save_model(model, scaler, model_path, scaler_path):
        print("✅ 模型保存成功！")
        
        # 验证保存的模型
        print("\n验证保存的模型...")
        loaded_model, loaded_scaler = load_model(model_path, scaler_path, 
                                                training_params['hidden_size'])
        
        if loaded_model is not None and loaded_scaler is not None:
            print("✅ 模型加载验证成功！")
            
            # 显示文件信息
            print(f"\n生成的文件:")
            print(f"  📄 {model_path} - 训练好的LSTM模型")
            print(f"  📄 {scaler_path} - 数据标准化器")
            
            # 显示文件大小
            try:
                model_size = os.path.getsize(model_path) / 1024  # KB
                scaler_size = os.path.getsize(scaler_path) / 1024  # KB
                print(f"  模型文件大小: {model_size:.1f} KB")
                print(f"  标准化器文件大小: {scaler_size:.1f} KB")
            except:
                pass
            
            return True
        else:
            print("❌ 模型加载验证失败")
            return False
    else:
        print("❌ 模型保存失败")
        return False


def quick_test():
    """快速测试已保存的模型"""
    print("\n" + "="*60)
    print("快速测试已保存的模型")
    print("="*60)
    
    model_path = 'displacement_model.pth'
    scaler_path = 'displacement_scaler.pkl'
    
    if not os.path.exists(model_path) or not os.path.exists(scaler_path):
        print("❌ 找不到已保存的模型文件")
        return False
    
    # 加载模型
    model, scaler = load_model(model_path, scaler_path, hidden_size=96)
    
    if model is None or scaler is None:
        print("❌ 模型加载失败")
        return False
    
    print("✅ 模型加载成功！")
    
    # 显示模型信息
    info = get_model_info(model, scaler)
    print("\n模型信息:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    # 简单的预测测试
    try:
        import torch
        import numpy as np
        
        print("\n进行简单预测测试...")
        
        # 创建测试数据（25个随机值）
        test_data = np.random.randn(25)
        normalized_data = scaler.transform(test_data.reshape(-1, 1)).flatten()
        
        # 转换为张量
        input_tensor = torch.FloatTensor(normalized_data).unsqueeze(0).unsqueeze(-1)
        
        # 进行预测
        with torch.no_grad():
            prediction = model(input_tensor).item()
        
        # 反标准化
        prediction_real = scaler.inverse_transform([[prediction]])[0, 0]
        
        print(f"测试预测结果: {prediction_real:.6f}")
        print("✅ 预测测试成功！")
        
        return True
        
    except Exception as e:
        print(f"❌ 预测测试失败: {e}")
        return False


if __name__ == "__main__":
    print("LSTM模型训练和保存程序")
    
    # 主训练流程
    success = main()
    
    if success:
        # 快速测试
        quick_test()
        
        print("\n" + "="*60)
        print("🎉 模型训练和保存完成！")
        print("\n现在可以使用以下方式加载模型:")
        print("```python")
        print("from real_time_train import load_model")
        print("model, scaler = load_model('displacement_model.pth', 'displacement_scaler.pkl')")
        print("```")
        print("\n或者直接运行:")
        print("  python integrated_real_time_demo.py  # 完整演示")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("❌ 模型训练失败")
        print("请检查:")
        print("  1. 训练数据文件 v1.txt 是否存在")
        print("  2. 运行 collect_training_data.py 收集训练数据")
        print("  3. 检查Python环境和依赖包")
        print("="*60)
